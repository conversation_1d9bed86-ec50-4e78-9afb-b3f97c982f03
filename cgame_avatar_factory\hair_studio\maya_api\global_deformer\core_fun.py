# Import built-in modules
from functools import partial
import logging

# Import third-party modules
import maya.cmds as cmds
import maya.mel as mel

# Import local modules
from cgame_avatar_factory.hair_studio.maya_api.global_deformer import DEFAULT_DISTANCE
from cgame_avatar_factory.hair_studio.maya_api.global_deformer import DEFAULT_SPANS


############################################## public fun for manager ###############################################
def set_deformer_range(deformer_name, value):
    cmds.setAttr("{}.dropoffDistance[0]".format(deformer_name), value)


def set_affected_face(deformer_name, affected_mesh_faces):
    is_success = False

    deformer_mesh = cmds.listConnections("{0}.outputGeometry".format(deformer_name), d=1)[0]

    # NOTE: checkk select face is belong to deformer_mesh
    affected_mesh_faces = [f for f in affected_mesh_faces if deformer_mesh in f.split(".")[0]]

    vert_num = cmds.polyEvaluate(deformer_mesh, vertex=True)
    if not vert_num:
        cmds.error("deformer_mesh  = {} has no vertex, please select face to end!".format(deformer_mesh))
        return

    cmds.percent(deformer_name, "{}.vtx[0:{}]".format(deformer_mesh, int(vert_num) - 1), v=0)

    verts = cmds.polyListComponentConversion(affected_mesh_faces, toVertex=True)
    if not verts:
        cmds.error("No vertices found in selection!")
        return is_success

    try:
        cmds.percent(deformer_name, verts, v=1)
        is_success = True
    except Exception as e:
        cmds.error("Failed to set deformer weights: {}".format(e))
        return is_success

    return is_success


def create_curve_for_sels(sels=None):
    cmds.warning("[GlobalDeformer] will create curve for selected faces!")
    cmds.inViewMessage(amg="create_curve_on_menu: 输入对象数 = {}".format(""), pos="topCenter", fade=True)
    # 0. selected face to shell ?

    if sels is None:
        sels = get_seles_long()

    # 1. convert select to edge perimeter
    _convert_sel_to_edge_perimeter(sels)
    # 2. polygon to curve
    mesh_curve = _poly_to_curve()

    # 3. average curve
    average_curve = _average_all_curve(mesh_curve, "{}_average".format(mesh_curve))
    cmds.hide(average_curve)

    # # offset curve
    # offset_curve = _offset_curve(average_curve, "{}_offset".format(average_curve))
    # cmds.hide(offset_curve)

    # # 4. recalculate curve
    # generated_curve = _rebuild_curve(offset_curve, "{}_rebuild".format(offset_curve))
    generated_curve, rebuild_attr, offset_attr = combine_offset_rebuild_curve(
        average_curve,
        "{}_rebuild_offset".format(average_curve),
    )

    # 5. set curve show way
    _set_curve_color(generated_curve)
    create_curve_params(rebuild_attr, offset_attr)

    # hub_ui = create_curve_param(average_curve, generated_curve)

    return generated_curve


def create_deformer_maya(mesh_node, curve_node, mesh_faces=None):
    # TODO: 后续在此绑定/创建变形器的逻辑
    # e.g. bind curve and mesh
    wire_deformer = cmds.wire(mesh_node, w=curve_node, gw=False, en=1.0, ce=0.0, li=0.0)
    if not wire_deformer:
        cmds.inViewMessage(amg="创建变形器失败", pos="topCenter", fade=True)
        return
    wire_deformer = wire_deformer[0]

    cmds.setAttr(wire_deformer + ".dropoffDistance[0]", 10)

    cmds.inViewMessage(amg="已经创建了wire deformer", pos="topCenter", fade=True)

    # TODO:set wire deformer weight for face
    # convert face to vertex and set weight
    if mesh_faces:
        vert_num = cmds.polyEvaluate(mesh_node, vertex=True)
        if not vert_num:
            cmds.error("deformer_mesh  = {} has no vertex, please select face to end!".format(mesh_node))
            return
        cmds.percent(wire_deformer, "{}.vtx[0:{}]".format(mesh_node, int(vert_num) - 1), v=0)

        verts = cmds.polyListComponentConversion(mesh_faces, toVertex=True)
        if not verts:
            cmds.error("No vertices found in selection!")
            return
        cmds.percent(wire_deformer, verts, v=1)

    # make curve points editable, select curve cv control
    mel.eval(
        """
        doMenuNURBComponentSelection("{0}", "controlVertex");
        select -r {0}.cv[0] ;
        """.format(
            curve_node
        )
    )
    return wire_deformer


def resolve_selection_type(return_all=False):
    """
    获取当前选择的详细信息。

    参数:
        return_all (bool):
            - False(默认): 返回第一个选中项的三元组 (sel_obj, obj_type, component_type)
            - True: 返回列表 [ (sel_obj, obj_type, component_type), ... ]，包含所有选中项

    返回值:
        - 当 return_all=False 时: ((sel_obj, obj_type, component_type), 1)
          若无选择: ((None, None, None), 0)
        - 当 return_all=True 时: ([ (sel_obj, obj_type, component_type), ... ], selected_count)
          其中 selected_count 计算规则：
            * 若选择为 mesh 组件（face/vertex/edge），按“所属mesh去重”的数量计数
              例如: obj1.f[0], obj1.f[1] -> 计数为 1；obj1.f[0], obj2.f[1] -> 计数为 2
            * 其他资产类型（如 transform、mesh 整体、nurbsCurve 等），每个选中项计数 +1

    兼容性:
        请注意：函数现在总是会同时返回“选中数量”。原先仅解包3个返回值的调用需要同步调整。
    """
    selection = cmds.ls(selection=True, long=True) or []

    def inspect_one(sel_obj):
        component_type = None
        obj_type = None

        # 组件选择（面、点、边）
        if "." in sel_obj:
            obj_path, component_part = sel_obj.split(".", 1)
            if component_part.startswith("f["):
                component_type = "face"
            elif component_part.startswith("vtx["):
                component_type = "vertex"
            elif component_part.startswith("e["):
                component_type = "edge"

            shapes = cmds.listRelatives(obj_path, shapes=True, fullPath=True) or []
            if shapes:
                obj_type = cmds.objectType(shapes[0])
            else:
                obj_type = cmds.objectType(obj_path)
        else:
            # 普通对象
            obj_type = cmds.objectType(sel_obj)
            shapes = cmds.listRelatives(sel_obj, shapes=True, fullPath=True) or []
            if shapes:
                obj_type = cmds.objectType(shapes[0])

        return sel_obj, obj_type, component_type

    def base_object_path(sel_obj):
        """返回基础物体长路径（若为组件则去掉 . 后缀）。"""
        return sel_obj.split(".", 1)[0] if "." in sel_obj else sel_obj

    # 全量解析
    parsed = [inspect_one(s) for s in selection]

    # 计算“选中物体数量”
    # 规则：
    # - mesh 组件（face/vertex/edge）：按所属 mesh 去重计数
    # - 其他资产：每个选中项 +1
    comp_types = {"face", "vertex", "edge"}
    component_mesh_set = set()
    non_component_count = 0

    for sel_obj, obj_type, component_type in parsed:
        if component_type in comp_types:
            component_mesh_set.add(base_object_path(sel_obj))
        else:
            # 非组件，直接按项计数
            non_component_count += 1

    total_selected_objects = len(component_mesh_set) + non_component_count

    if return_all:
        return parsed, total_selected_objects

    # 兼容旧逻辑: 无选择返回 (None, None, None)，否则仅返回第一个
    if not selection:
        return (None, None, None), 0
    first = parsed[0]
    # return_all=False 时，按需求固定返回 1
    return first, 1


############################################## public fun for manager ###############################################


############################################## inner function #########################################
def combine_offset_rebuild_curve(src_curve, output_curve_name=None):
    # 1. offset
    offsets = cmds.offsetCurve(
        src_curve,
        ch=1,
        rn=0,
        cb=2,
        st=True,
        cl=True,
        cr=0,
        d=DEFAULT_DISTANCE,
        tol=0.01,
        sd=5,
        ugn=False,
    )

    if not offsets or not len(offsets) == 2:
        cmds.warning("offset curve failed")
        return
    offset_curve = offsets[0]
    offset_node = offsets[1]

    # 2.  rebuild
    rebuilds = cmds.rebuildCurve(offset_curve, ch=0, rpo=1, rt=0, end=1, kr=0, kcp=0, kep=1, kt=1, s=20, d=3, tol=0.01)

    if not rebuilds or not len(rebuilds) == 2:
        cmds.warning("rebuild curve failed")
        return
    rebuild_curve = rebuilds[0]
    rebuild_node = rebuilds[1]
    if output_curve_name and output_curve_name != rebuild_curve and not cmds.objExists(output_curve_name):
        cmds.rename(rebuild_curve, output_curve_name)
        rebuild_curve = output_curve_name
        cmds.warning("rebuild curve renamed to {}".format(rebuild_curve))

    # attr
    cmds.setAttr(rebuild_node + ".spans", DEFAULT_SPANS)
    # cmds.setAttr(offset_node + ".distance", ui_distance)

    return rebuild_curve, "{0}.{1}".format(rebuild_node, "spans"), "{0}.{1}".format(offset_node, "distance")


def create_curve_params(point_num_attr, offset_attr):
    cmds.inViewMessage(amg="拖动滑块重新调整曲线顶点数量", pos="bottomCenter", fade=True)
    logger = logging.getLogger(__name__)

    curve_ui_name = "curve_points"
    offset_ui_name = "offset_distance"

    def on_drag(ui_name, point_num_attr, *args):
        logger.debug("on_drag, will rebuild curve for {0}, attr = {1}".format(ui_name, point_num_attr))
        value = cmds.hudSlider(ui_name, query=True, v=True)
        cmds.setAttr(point_num_attr, value)

    # check if ui exists
    if curve_ui_name in cmds.headsUpDisplay(listHeadsUpDisplays=True):
        cmds.headsUpDisplay(curve_ui_name, remove=True)
    if offset_ui_name in cmds.headsUpDisplay(listHeadsUpDisplays=True):
        cmds.headsUpDisplay(offset_ui_name, remove=True)

    cmds.hudSlider(
        curve_ui_name,
        s=6,  # section位置
        b=5,  # block位置
        vis=True,
        label="curve points:",
        sl=150,
        labelWidth=100,
        valueWidth=50,
        type="int",
        min=4,
        max=15,
        value=DEFAULT_SPANS,
        si=1,
        blockSize="medium",
        blockAlignment="center",
        dragCommand=partial(on_drag, curve_ui_name, point_num_attr),
    )

    cmds.hudSlider(
        offset_ui_name,
        s=6,  # section位置
        b=4,  # block位置
        vis=True,
        label="offset distance:",
        sl=150,
        labelWidth=100,
        valueWidth=50,
        type="float",
        min=-1.0,
        max=1.0,
        value=DEFAULT_DISTANCE,
        si=0.1,
        blockSize="medium",
        blockAlignment="center",
        dragCommand=partial(on_drag, offset_ui_name, offset_attr),
    )

    def hideHud(ui_name, *args):
        if cmds.headsUpDisplay(ui_name, exists=True):
            cmds.headsUpDisplay(ui_name, remove=True)

    jobId = cmds.scriptJob(event=["SelectionChanged", partial(hideHud, curve_ui_name)], runOnce=True)
    # TODO: for safty, finally remove jobid

    def hideHud_offset(*args):
        if cmds.headsUpDisplay(offset_ui_name, exists=True):
            cmds.headsUpDisplay(offset_ui_name, remove=True)

    jobId_offset = cmds.scriptJob(event=["SelectionChanged", hideHud_offset], runOnce=True)
    # TODO: for safty, finally remove jobid
    logger.debug(
        "create_curve_param done, curve_ui_name = {0}, offset_ui_name = {1}".format(curve_ui_name, offset_ui_name),
    )
    return curve_ui_name


def resolve_curve_mesh_from_sel():
    sels, sel_count = resolve_selection_type(True)

    cmds.inViewMessage(amg="create_deformer_on_menu: 输入对象数 = {}".format(sel_count), pos="topCenter", fade=True)
    if sel_count < 2:
        cmds.inViewMessage(amg="请选择一个曲线和一个模型", pos="topCenter", fade=True)
        return

    curve_node = None
    mesh_node = None
    is_component_sel = False  # 是否从组件选择中解析出的mesh
    mesh_faces = []  # 记录选中的面（展开后的组件列表）

    def base_object_path(sel_obj):
        """若是组件选择，返回其对象路径，否则返回自身。"""
        return sel_obj.split(".", 1)[0] if "." in sel_obj else sel_obj

    # 在选择中查找第一个曲线与第一个网格
    for sel_obj, obj_type, component_type in sels:
        # 找曲线
        if curve_node is None and obj_type == "nurbsCurve":
            curve_node = base_object_path(sel_obj)
            continue
        # 找网格（mesh 或其组件）
        if mesh_node is None and obj_type == "mesh":
            mesh_node = base_object_path(sel_obj)
            if component_type is not None:
                is_component_sel = True
                # 如果是面组件，展开并记录
                if component_type == "face":
                    try:
                        expanded = cmds.filterExpand(sel_obj, selectionMask=34) or []  # 34: faces
                        mesh_faces.extend(expanded)
                    except Exception:
                        # 兜底：直接记录原始组件
                        mesh_faces.append(sel_obj)
        elif obj_type == "mesh" and component_type == "face":
            # 另一种情况：先遇到曲线，再遇到面的情况下，mesh_node 已存在或未设置
            base = base_object_path(sel_obj)
            if mesh_node is None:
                mesh_node = base
            if base_object_path(sel_obj) == mesh_node:
                is_component_sel = True
                try:
                    expanded = cmds.filterExpand(sel_obj, selectionMask=34) or []
                    mesh_faces.extend(expanded)
                except Exception:
                    mesh_faces.append(sel_obj)

    # 校验必须各找到一个
    if not curve_node or not mesh_node:
        cmds.inViewMessage(amg="需要选择一个曲线与一个模型", pos="topCenter", fade=True)
        return

    # 若选择超过两个，这里也可以弹出提示或仅取第一对
    # 这里选择仅取第一对
    # 去重整理面列表
    if mesh_faces:
        mesh_faces = sorted(set(mesh_faces))

    cmds.warning(
        "Resolved selection -> curve: {}, mesh: {}, via_component: {}, faces: {}".format(
            curve_node,
            mesh_node,
            is_component_sel,
            len(mesh_faces),
        ),
    )

    return curve_node, mesh_node, mesh_faces


def get_seles_long():
    sels = cmds.ls(sl=1, long=1)
    return sels


def _convert_sel_to_edge_perimeter(sels):
    mel.eval("ConvertSelectionToEdgePerimeter;")
    sel_edge = cmds.ls(sl=1, long=1)
    cmds.warning("convert_sel_to_edge_perimeter, input sels = {0} sel_edge = {1}".format(sels, sel_edge))


def _poly_to_curve():
    cmds.inViewMessage(amg="poly_to_curve", pos="topCenter", fade=True)
    bound_curve, poly2curve_node = cmds.polyToCurve(form=3, degree=3, conformToSmoothMeshPreview=1)

    return bound_curve


def _average_all_curve(closed_curve_name, output_name="curve_average"):
    # Import built-in modules
    import math

    # TODO: handle curve node is not in the tip or tail position
    def get_curve_cvs_points(curve_name):
        """
        获取曲线所有CV点坐标，返回[(x,y,z), ...]列表
        """
        cvs = cmds.ls(curve_name + ".cv[*]", fl=True)
        points = [cmds.pointPosition(cv, w=True) for cv in cvs]
        return [tuple(p) for p in points]

    def create_curve_from_points(points, name="midLineCurve"):
        degree = 3 if len(points) >= 4 else 1
        curve = cmds.curve(p=points, degree=degree, name=name)
        return curve

    def generate_midline_curve_from_closed_long_curve(closed_curve_name, output_curve_name="midLineCurve"):
        points = get_curve_cvs_points(closed_curve_name)
        cmds.delete(closed_curve_name)
        num_points = len(points)

        if num_points % 2 != 0:
            cmds.warning("曲线CV点数不是偶数，无法均分为两条长边，已自动减去一个点")
            num_points -= 1

        half = num_points // 2

        edge1 = points[:half]
        edge2 = points[half:]

        def dist(a, b):
            return math.sqrt((a[0] - b[0]) ** 2 + (a[1] - b[1]) ** 2 + (a[2] - b[2]) ** 2)

        dist_start = dist(edge1[0], edge2[0])
        dist_end = dist(edge1[0], edge2[-1])
        if dist_end < dist_start:
            edge2 = list(reversed(edge2))
        mid_points = [((a[0] + b[0]) / 2.0, (a[1] + b[1]) / 2.0, (a[2] + b[2]) / 2.0) for a, b in zip(edge1, edge2)]

        new_curve = create_curve_from_points(mid_points, output_curve_name)

        return new_curve

    return generate_midline_curve_from_closed_long_curve(closed_curve_name, output_name)


def _set_curve_color(curve_name):
    shape_node = cmds.listRelatives(curve_name, shapes=True, f=True)[0]
    cmds.setAttr(shape_node + ".lineWidth", 5)
    cmds.setAttr(shape_node + ".alwaysDrawOnTop", 1)
    cmds.setAttr(shape_node + ".overrideEnabled", 1)
    # Enable RGB overrides
    cmds.setAttr(shape_node + ".overrideRGBColors", 1)

    cmds.setAttr(shape_node + ".overrideColorRGB", 1, 1, 0)

    cmds.setAttr(shape_node + ".dispCV", 1)
    cmds.setAttr(shape_node + ".dispHull", 1)


############################################## inner function #########################################
